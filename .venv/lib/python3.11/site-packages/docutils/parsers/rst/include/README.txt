============================================
 ``docutils/parsers/rst/include`` Directory
============================================

This directory contains standard data files intended for inclusion in
reStructuredText documents.  To access these files, use the "include"
directive with the special syntax for standard "include" data files,
angle brackets around the file name::

    .. include:: <isonum.txt>

See the documentation for the `"include" directive`__ and
`reStructuredText Standard Definition Files`__ for
details.

__ https://docutils.sourceforge.io/docs/ref/rst/directives.html#include
__ https://docutils.sourceforge.io/docs/ref/rst/definitions.html
