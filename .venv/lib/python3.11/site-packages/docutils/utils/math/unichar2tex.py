# LaTeX math to Unicode symbols translation table
# for use with the translate() method of unicode objects.
# Generated with ``write_unichar2tex.py`` from the data in
# http://milde.users.sourceforge.net/LUCR/Math/

# Includes commands from: standard LaTeX, amssymb, amsmath

uni2tex_table = {
0xa0: '~',
0xa3: '\\pounds ',
0xa5: '\\yen ',
0xa7: '\\S ',
0xac: '\\neg ',
0xb1: '\\pm ',
0xb6: '\\P ',
0xd7: '\\times ',
0xf0: '\\eth ',
0xf7: '\\div ',
0x131: '\\imath ',
0x237: '\\jmath ',
0x393: '\\Gamma ',
0x394: '\\Delta ',
0x398: '\\Theta ',
0x39b: '\\Lambda ',
0x39e: '\\Xi ',
0x3a0: '\\Pi ',
0x3a3: '\\Sigma ',
0x3a5: '\\Upsilon ',
0x3a6: '\\Phi ',
0x3a8: '\\Psi ',
0x3a9: '\\Omega ',
0x3b1: '\\alpha ',
0x3b2: '\\beta ',
0x3b3: '\\gamma ',
0x3b4: '\\delta ',
0x3b5: '\\varepsilon ',
0x3b6: '\\zeta ',
0x3b7: '\\eta ',
0x3b8: '\\theta ',
0x3b9: '\\iota ',
0x3ba: '\\kappa ',
0x3bb: '\\lambda ',
0x3bc: '\\mu ',
0x3bd: '\\nu ',
0x3be: '\\xi ',
0x3c0: '\\pi ',
0x3c1: '\\rho ',
0x3c2: '\\varsigma ',
0x3c3: '\\sigma ',
0x3c4: '\\tau ',
0x3c5: '\\upsilon ',
0x3c6: '\\varphi ',
0x3c7: '\\chi ',
0x3c8: '\\psi ',
0x3c9: '\\omega ',
0x3d1: '\\vartheta ',
0x3d5: '\\phi ',
0x3d6: '\\varpi ',
0x3dd: '\\digamma ',
0x3f0: '\\varkappa ',
0x3f1: '\\varrho ',
0x3f5: '\\epsilon ',
0x3f6: '\\backepsilon ',
0x2001: '\\quad ',
0x2003: '\\quad ',
0x2006: '\\, ',
0x2016: '\\| ',
0x2020: '\\dagger ',
0x2021: '\\ddagger ',
0x2022: '\\bullet ',
0x2026: '\\ldots ',
0x2032: '\\prime ',
0x2035: '\\backprime ',
0x205f: '\\: ',
0x2102: '\\mathbb{C}',
0x210b: '\\mathcal{H}',
0x210c: '\\mathfrak{H}',
0x210d: '\\mathbb{H}',
0x210f: '\\hslash ',
0x2110: '\\mathcal{I}',
0x2111: '\\Im ',
0x2112: '\\mathcal{L}',
0x2113: '\\ell ',
0x2115: '\\mathbb{N}',
0x2118: '\\wp ',
0x2119: '\\mathbb{P}',
0x211a: '\\mathbb{Q}',
0x211b: '\\mathcal{R}',
0x211c: '\\Re ',
0x211d: '\\mathbb{R}',
0x2124: '\\mathbb{Z}',
0x2127: '\\mho ',
0x2128: '\\mathfrak{Z}',
0x212c: '\\mathcal{B}',
0x212d: '\\mathfrak{C}',
0x2130: '\\mathcal{E}',
0x2131: '\\mathcal{F}',
0x2132: '\\Finv ',
0x2133: '\\mathcal{M}',
0x2135: '\\aleph ',
0x2136: '\\beth ',
0x2137: '\\gimel ',
0x2138: '\\daleth ',
0x2190: '\\leftarrow ',
0x2191: '\\uparrow ',
0x2192: '\\rightarrow ',
0x2193: '\\downarrow ',
0x2194: '\\leftrightarrow ',
0x2195: '\\updownarrow ',
0x2196: '\\nwarrow ',
0x2197: '\\nearrow ',
0x2198: '\\searrow ',
0x2199: '\\swarrow ',
0x219a: '\\nleftarrow ',
0x219b: '\\nrightarrow ',
0x219e: '\\twoheadleftarrow ',
0x21a0: '\\twoheadrightarrow ',
0x21a2: '\\leftarrowtail ',
0x21a3: '\\rightarrowtail ',
0x21a6: '\\mapsto ',
0x21a9: '\\hookleftarrow ',
0x21aa: '\\hookrightarrow ',
0x21ab: '\\looparrowleft ',
0x21ac: '\\looparrowright ',
0x21ad: '\\leftrightsquigarrow ',
0x21ae: '\\nleftrightarrow ',
0x21b0: '\\Lsh ',
0x21b1: '\\Rsh ',
0x21b6: '\\curvearrowleft ',
0x21b7: '\\curvearrowright ',
0x21ba: '\\circlearrowleft ',
0x21bb: '\\circlearrowright ',
0x21bc: '\\leftharpoonup ',
0x21bd: '\\leftharpoondown ',
0x21be: '\\upharpoonright ',
0x21bf: '\\upharpoonleft ',
0x21c0: '\\rightharpoonup ',
0x21c1: '\\rightharpoondown ',
0x21c2: '\\downharpoonright ',
0x21c3: '\\downharpoonleft ',
0x21c4: '\\rightleftarrows ',
0x21c6: '\\leftrightarrows ',
0x21c7: '\\leftleftarrows ',
0x21c8: '\\upuparrows ',
0x21c9: '\\rightrightarrows ',
0x21ca: '\\downdownarrows ',
0x21cb: '\\leftrightharpoons ',
0x21cc: '\\rightleftharpoons ',
0x21cd: '\\nLeftarrow ',
0x21ce: '\\nLeftrightarrow ',
0x21cf: '\\nRightarrow ',
0x21d0: '\\Leftarrow ',
0x21d1: '\\Uparrow ',
0x21d2: '\\Rightarrow ',
0x21d3: '\\Downarrow ',
0x21d4: '\\Leftrightarrow ',
0x21d5: '\\Updownarrow ',
0x21da: '\\Lleftarrow ',
0x21db: '\\Rrightarrow ',
0x21dd: '\\rightsquigarrow ',
0x21e0: '\\dashleftarrow ',
0x21e2: '\\dashrightarrow ',
0x2200: '\\forall ',
0x2201: '\\complement ',
0x2202: '\\partial ',
0x2203: '\\exists ',
0x2204: '\\nexists ',
0x2205: '\\emptyset ',
0x2207: '\\nabla ',
0x2208: '\\in ',
0x2209: '\\notin ',
0x220b: '\\ni ',
0x220f: '\\prod ',
0x2210: '\\coprod ',
0x2211: '\\sum ',
0x2212: '-',
0x2213: '\\mp ',
0x2214: '\\dotplus ',
0x2215: '\\slash ',
0x2216: '\\smallsetminus ',
0x2217: '\\ast ',
0x2218: '\\circ ',
0x2219: '\\bullet ',
0x221a: '\\surd ',
0x221b: '\\sqrt[3] ',
0x221c: '\\sqrt[4] ',
0x221d: '\\propto ',
0x221e: '\\infty ',
0x2220: '\\angle ',
0x2221: '\\measuredangle ',
0x2222: '\\sphericalangle ',
0x2223: '\\mid ',
0x2224: '\\nmid ',
0x2225: '\\parallel ',
0x2226: '\\nparallel ',
0x2227: '\\wedge ',
0x2228: '\\vee ',
0x2229: '\\cap ',
0x222a: '\\cup ',
0x222b: '\\int ',
0x222c: '\\iint ',
0x222d: '\\iiint ',
0x222e: '\\oint ',
0x2234: '\\therefore ',
0x2235: '\\because ',
0x2236: ':',
0x223c: '\\sim ',
0x223d: '\\backsim ',
0x2240: '\\wr ',
0x2241: '\\nsim ',
0x2242: '\\eqsim ',
0x2243: '\\simeq ',
0x2245: '\\cong ',
0x2247: '\\ncong ',
0x2248: '\\approx ',
0x224a: '\\approxeq ',
0x224d: '\\asymp ',
0x224e: '\\Bumpeq ',
0x224f: '\\bumpeq ',
0x2250: '\\doteq ',
0x2251: '\\Doteq ',
0x2252: '\\fallingdotseq ',
0x2253: '\\risingdotseq ',
0x2256: '\\eqcirc ',
0x2257: '\\circeq ',
0x225c: '\\triangleq ',
0x2260: '\\neq ',
0x2261: '\\equiv ',
0x2264: '\\leq ',
0x2265: '\\geq ',
0x2266: '\\leqq ',
0x2267: '\\geqq ',
0x2268: '\\lneqq ',
0x2269: '\\gneqq ',
0x226a: '\\ll ',
0x226b: '\\gg ',
0x226c: '\\between ',
0x226e: '\\nless ',
0x226f: '\\ngtr ',
0x2270: '\\nleq ',
0x2271: '\\ngeq ',
0x2272: '\\lesssim ',
0x2273: '\\gtrsim ',
0x2276: '\\lessgtr ',
0x2277: '\\gtrless ',
0x227a: '\\prec ',
0x227b: '\\succ ',
0x227c: '\\preccurlyeq ',
0x227d: '\\succcurlyeq ',
0x227e: '\\precsim ',
0x227f: '\\succsim ',
0x2280: '\\nprec ',
0x2281: '\\nsucc ',
0x2282: '\\subset ',
0x2283: '\\supset ',
0x2286: '\\subseteq ',
0x2287: '\\supseteq ',
0x2288: '\\nsubseteq ',
0x2289: '\\nsupseteq ',
0x228a: '\\subsetneq ',
0x228b: '\\supsetneq ',
0x228e: '\\uplus ',
0x228f: '\\sqsubset ',
0x2290: '\\sqsupset ',
0x2291: '\\sqsubseteq ',
0x2292: '\\sqsupseteq ',
0x2293: '\\sqcap ',
0x2294: '\\sqcup ',
0x2295: '\\oplus ',
0x2296: '\\ominus ',
0x2297: '\\otimes ',
0x2298: '\\oslash ',
0x2299: '\\odot ',
0x229a: '\\circledcirc ',
0x229b: '\\circledast ',
0x229d: '\\circleddash ',
0x229e: '\\boxplus ',
0x229f: '\\boxminus ',
0x22a0: '\\boxtimes ',
0x22a1: '\\boxdot ',
0x22a2: '\\vdash ',
0x22a3: '\\dashv ',
0x22a4: '\\top ',
0x22a5: '\\bot ',
0x22a7: '\\models ',
0x22a8: '\\vDash ',
0x22a9: '\\Vdash ',
0x22aa: '\\Vvdash ',
0x22ac: '\\nvdash ',
0x22ad: '\\nvDash ',
0x22ae: '\\nVdash ',
0x22af: '\\nVDash ',
0x22b2: '\\vartriangleleft ',
0x22b3: '\\vartriangleright ',
0x22b4: '\\trianglelefteq ',
0x22b5: '\\trianglerighteq ',
0x22b8: '\\multimap ',
0x22ba: '\\intercal ',
0x22bb: '\\veebar ',
0x22bc: '\\barwedge ',
0x22c0: '\\bigwedge ',
0x22c1: '\\bigvee ',
0x22c2: '\\bigcap ',
0x22c3: '\\bigcup ',
0x22c4: '\\diamond ',
0x22c5: '\\cdot ',
0x22c6: '\\star ',
0x22c7: '\\divideontimes ',
0x22c8: '\\bowtie ',
0x22c9: '\\ltimes ',
0x22ca: '\\rtimes ',
0x22cb: '\\leftthreetimes ',
0x22cc: '\\rightthreetimes ',
0x22cd: '\\backsimeq ',
0x22ce: '\\curlyvee ',
0x22cf: '\\curlywedge ',
0x22d0: '\\Subset ',
0x22d1: '\\Supset ',
0x22d2: '\\Cap ',
0x22d3: '\\Cup ',
0x22d4: '\\pitchfork ',
0x22d6: '\\lessdot ',
0x22d7: '\\gtrdot ',
0x22d8: '\\lll ',
0x22d9: '\\ggg ',
0x22da: '\\lesseqgtr ',
0x22db: '\\gtreqless ',
0x22de: '\\curlyeqprec ',
0x22df: '\\curlyeqsucc ',
0x22e0: '\\npreceq ',
0x22e1: '\\nsucceq ',
0x22e6: '\\lnsim ',
0x22e7: '\\gnsim ',
0x22e8: '\\precnsim ',
0x22e9: '\\succnsim ',
0x22ea: '\\ntriangleleft ',
0x22eb: '\\ntriangleright ',
0x22ec: '\\ntrianglelefteq ',
0x22ed: '\\ntrianglerighteq ',
0x22ee: '\\vdots ',
0x22ef: '\\cdots ',
0x22f1: '\\ddots ',
0x2308: '\\lceil ',
0x2309: '\\rceil ',
0x230a: '\\lfloor ',
0x230b: '\\rfloor ',
0x231c: '\\ulcorner ',
0x231d: '\\urcorner ',
0x231e: '\\llcorner ',
0x231f: '\\lrcorner ',
0x2322: '\\frown ',
0x2323: '\\smile ',
0x23aa: '\\bracevert ',
0x23b0: '\\lmoustache ',
0x23b1: '\\rmoustache ',
0x23d0: '\\arrowvert ',
0x23de: '\\overbrace ',
0x23df: '\\underbrace ',
0x24c7: '\\circledR ',
0x24c8: '\\circledS ',
0x25b2: '\\blacktriangle ',
0x25b3: '\\bigtriangleup ',
0x25b7: '\\triangleright ',
0x25bc: '\\blacktriangledown ',
0x25bd: '\\bigtriangledown ',
0x25c1: '\\triangleleft ',
0x25c7: '\\Diamond ',
0x25ca: '\\lozenge ',
0x25ef: '\\bigcirc ',
0x25fb: '\\square ',
0x25fc: '\\blacksquare ',
0x2605: '\\bigstar ',
0x2660: '\\spadesuit ',
0x2661: '\\heartsuit ',
0x2662: '\\diamondsuit ',
0x2663: '\\clubsuit ',
0x266d: '\\flat ',
0x266e: '\\natural ',
0x266f: '\\sharp ',
0x2713: '\\checkmark ',
0x2720: '\\maltese ',
0x27c2: '\\perp ',
0x27cb: '\\diagup ',
0x27cd: '\\diagdown ',
0x27e8: '\\langle ',
0x27e9: '\\rangle ',
0x27ee: '\\lgroup ',
0x27ef: '\\rgroup ',
0x27f5: '\\longleftarrow ',
0x27f6: '\\longrightarrow ',
0x27f7: '\\longleftrightarrow ',
0x27f8: '\\Longleftarrow ',
0x27f9: '\\Longrightarrow ',
0x27fa: '\\Longleftrightarrow ',
0x27fc: '\\longmapsto ',
0x29eb: '\\blacklozenge ',
0x29f5: '\\setminus ',
0x2a00: '\\bigodot ',
0x2a01: '\\bigoplus ',
0x2a02: '\\bigotimes ',
0x2a04: '\\biguplus ',
0x2a06: '\\bigsqcup ',
0x2a0c: '\\iiiint ',
0x2a3f: '\\amalg ',
0x2a5e: '\\doublebarwedge ',
0x2a7d: '\\leqslant ',
0x2a7e: '\\geqslant ',
0x2a85: '\\lessapprox ',
0x2a86: '\\gtrapprox ',
0x2a87: '\\lneq ',
0x2a88: '\\gneq ',
0x2a89: '\\lnapprox ',
0x2a8a: '\\gnapprox ',
0x2a8b: '\\lesseqqgtr ',
0x2a8c: '\\gtreqqless ',
0x2a95: '\\eqslantless ',
0x2a96: '\\eqslantgtr ',
0x2aaf: '\\preceq ',
0x2ab0: '\\succeq ',
0x2ab5: '\\precneqq ',
0x2ab6: '\\succneqq ',
0x2ab7: '\\precapprox ',
0x2ab8: '\\succapprox ',
0x2ab9: '\\precnapprox ',
0x2aba: '\\succnapprox ',
0x2ac5: '\\subseteqq ',
0x2ac6: '\\supseteqq ',
0x2acb: '\\subsetneqq ',
0x2acc: '\\supsetneqq ',
0x2b1c: '\\Box ',
0x1d400: '\\mathbf{A}',
0x1d401: '\\mathbf{B}',
0x1d402: '\\mathbf{C}',
0x1d403: '\\mathbf{D}',
0x1d404: '\\mathbf{E}',
0x1d405: '\\mathbf{F}',
0x1d406: '\\mathbf{G}',
0x1d407: '\\mathbf{H}',
0x1d408: '\\mathbf{I}',
0x1d409: '\\mathbf{J}',
0x1d40a: '\\mathbf{K}',
0x1d40b: '\\mathbf{L}',
0x1d40c: '\\mathbf{M}',
0x1d40d: '\\mathbf{N}',
0x1d40e: '\\mathbf{O}',
0x1d40f: '\\mathbf{P}',
0x1d410: '\\mathbf{Q}',
0x1d411: '\\mathbf{R}',
0x1d412: '\\mathbf{S}',
0x1d413: '\\mathbf{T}',
0x1d414: '\\mathbf{U}',
0x1d415: '\\mathbf{V}',
0x1d416: '\\mathbf{W}',
0x1d417: '\\mathbf{X}',
0x1d418: '\\mathbf{Y}',
0x1d419: '\\mathbf{Z}',
0x1d41a: '\\mathbf{a}',
0x1d41b: '\\mathbf{b}',
0x1d41c: '\\mathbf{c}',
0x1d41d: '\\mathbf{d}',
0x1d41e: '\\mathbf{e}',
0x1d41f: '\\mathbf{f}',
0x1d420: '\\mathbf{g}',
0x1d421: '\\mathbf{h}',
0x1d422: '\\mathbf{i}',
0x1d423: '\\mathbf{j}',
0x1d424: '\\mathbf{k}',
0x1d425: '\\mathbf{l}',
0x1d426: '\\mathbf{m}',
0x1d427: '\\mathbf{n}',
0x1d428: '\\mathbf{o}',
0x1d429: '\\mathbf{p}',
0x1d42a: '\\mathbf{q}',
0x1d42b: '\\mathbf{r}',
0x1d42c: '\\mathbf{s}',
0x1d42d: '\\mathbf{t}',
0x1d42e: '\\mathbf{u}',
0x1d42f: '\\mathbf{v}',
0x1d430: '\\mathbf{w}',
0x1d431: '\\mathbf{x}',
0x1d432: '\\mathbf{y}',
0x1d433: '\\mathbf{z}',
0x1d434: 'A',
0x1d435: 'B',
0x1d436: 'C',
0x1d437: 'D',
0x1d438: 'E',
0x1d439: 'F',
0x1d43a: 'G',
0x1d43b: 'H',
0x1d43c: 'I',
0x1d43d: 'J',
0x1d43e: 'K',
0x1d43f: 'L',
0x1d440: 'M',
0x1d441: 'N',
0x1d442: 'O',
0x1d443: 'P',
0x1d444: 'Q',
0x1d445: 'R',
0x1d446: 'S',
0x1d447: 'T',
0x1d448: 'U',
0x1d449: 'V',
0x1d44a: 'W',
0x1d44b: 'X',
0x1d44c: 'Y',
0x1d44d: 'Z',
0x1d44e: 'a',
0x1d44f: 'b',
0x1d450: 'c',
0x1d451: 'd',
0x1d452: 'e',
0x1d453: 'f',
0x1d454: 'g',
0x1d456: 'i',
0x1d457: 'j',
0x1d458: 'k',
0x1d459: 'l',
0x1d45a: 'm',
0x1d45b: 'n',
0x1d45c: 'o',
0x1d45d: 'p',
0x1d45e: 'q',
0x1d45f: 'r',
0x1d460: 's',
0x1d461: 't',
0x1d462: 'u',
0x1d463: 'v',
0x1d464: 'w',
0x1d465: 'x',
0x1d466: 'y',
0x1d467: 'z',
0x1d49c: '\\mathcal{A}',
0x1d49e: '\\mathcal{C}',
0x1d49f: '\\mathcal{D}',
0x1d4a2: '\\mathcal{G}',
0x1d4a5: '\\mathcal{J}',
0x1d4a6: '\\mathcal{K}',
0x1d4a9: '\\mathcal{N}',
0x1d4aa: '\\mathcal{O}',
0x1d4ab: '\\mathcal{P}',
0x1d4ac: '\\mathcal{Q}',
0x1d4ae: '\\mathcal{S}',
0x1d4af: '\\mathcal{T}',
0x1d4b0: '\\mathcal{U}',
0x1d4b1: '\\mathcal{V}',
0x1d4b2: '\\mathcal{W}',
0x1d4b3: '\\mathcal{X}',
0x1d4b4: '\\mathcal{Y}',
0x1d4b5: '\\mathcal{Z}',
0x1d504: '\\mathfrak{A}',
0x1d505: '\\mathfrak{B}',
0x1d507: '\\mathfrak{D}',
0x1d508: '\\mathfrak{E}',
0x1d509: '\\mathfrak{F}',
0x1d50a: '\\mathfrak{G}',
0x1d50d: '\\mathfrak{J}',
0x1d50e: '\\mathfrak{K}',
0x1d50f: '\\mathfrak{L}',
0x1d510: '\\mathfrak{M}',
0x1d511: '\\mathfrak{N}',
0x1d512: '\\mathfrak{O}',
0x1d513: '\\mathfrak{P}',
0x1d514: '\\mathfrak{Q}',
0x1d516: '\\mathfrak{S}',
0x1d517: '\\mathfrak{T}',
0x1d518: '\\mathfrak{U}',
0x1d519: '\\mathfrak{V}',
0x1d51a: '\\mathfrak{W}',
0x1d51b: '\\mathfrak{X}',
0x1d51c: '\\mathfrak{Y}',
0x1d51e: '\\mathfrak{a}',
0x1d51f: '\\mathfrak{b}',
0x1d520: '\\mathfrak{c}',
0x1d521: '\\mathfrak{d}',
0x1d522: '\\mathfrak{e}',
0x1d523: '\\mathfrak{f}',
0x1d524: '\\mathfrak{g}',
0x1d525: '\\mathfrak{h}',
0x1d526: '\\mathfrak{i}',
0x1d527: '\\mathfrak{j}',
0x1d528: '\\mathfrak{k}',
0x1d529: '\\mathfrak{l}',
0x1d52a: '\\mathfrak{m}',
0x1d52b: '\\mathfrak{n}',
0x1d52c: '\\mathfrak{o}',
0x1d52d: '\\mathfrak{p}',
0x1d52e: '\\mathfrak{q}',
0x1d52f: '\\mathfrak{r}',
0x1d530: '\\mathfrak{s}',
0x1d531: '\\mathfrak{t}',
0x1d532: '\\mathfrak{u}',
0x1d533: '\\mathfrak{v}',
0x1d534: '\\mathfrak{w}',
0x1d535: '\\mathfrak{x}',
0x1d536: '\\mathfrak{y}',
0x1d537: '\\mathfrak{z}',
0x1d538: '\\mathbb{A}',
0x1d539: '\\mathbb{B}',
0x1d53b: '\\mathbb{D}',
0x1d53c: '\\mathbb{E}',
0x1d53d: '\\mathbb{F}',
0x1d53e: '\\mathbb{G}',
0x1d540: '\\mathbb{I}',
0x1d541: '\\mathbb{J}',
0x1d542: '\\mathbb{K}',
0x1d543: '\\mathbb{L}',
0x1d544: '\\mathbb{M}',
0x1d546: '\\mathbb{O}',
0x1d54a: '\\mathbb{S}',
0x1d54b: '\\mathbb{T}',
0x1d54c: '\\mathbb{U}',
0x1d54d: '\\mathbb{V}',
0x1d54e: '\\mathbb{W}',
0x1d54f: '\\mathbb{X}',
0x1d550: '\\mathbb{Y}',
0x1d55c: '\\Bbbk ',
0x1d5a0: '\\mathsf{A}',
0x1d5a1: '\\mathsf{B}',
0x1d5a2: '\\mathsf{C}',
0x1d5a3: '\\mathsf{D}',
0x1d5a4: '\\mathsf{E}',
0x1d5a5: '\\mathsf{F}',
0x1d5a6: '\\mathsf{G}',
0x1d5a7: '\\mathsf{H}',
0x1d5a8: '\\mathsf{I}',
0x1d5a9: '\\mathsf{J}',
0x1d5aa: '\\mathsf{K}',
0x1d5ab: '\\mathsf{L}',
0x1d5ac: '\\mathsf{M}',
0x1d5ad: '\\mathsf{N}',
0x1d5ae: '\\mathsf{O}',
0x1d5af: '\\mathsf{P}',
0x1d5b0: '\\mathsf{Q}',
0x1d5b1: '\\mathsf{R}',
0x1d5b2: '\\mathsf{S}',
0x1d5b3: '\\mathsf{T}',
0x1d5b4: '\\mathsf{U}',
0x1d5b5: '\\mathsf{V}',
0x1d5b6: '\\mathsf{W}',
0x1d5b7: '\\mathsf{X}',
0x1d5b8: '\\mathsf{Y}',
0x1d5b9: '\\mathsf{Z}',
0x1d5ba: '\\mathsf{a}',
0x1d5bb: '\\mathsf{b}',
0x1d5bc: '\\mathsf{c}',
0x1d5bd: '\\mathsf{d}',
0x1d5be: '\\mathsf{e}',
0x1d5bf: '\\mathsf{f}',
0x1d5c0: '\\mathsf{g}',
0x1d5c1: '\\mathsf{h}',
0x1d5c2: '\\mathsf{i}',
0x1d5c3: '\\mathsf{j}',
0x1d5c4: '\\mathsf{k}',
0x1d5c5: '\\mathsf{l}',
0x1d5c6: '\\mathsf{m}',
0x1d5c7: '\\mathsf{n}',
0x1d5c8: '\\mathsf{o}',
0x1d5c9: '\\mathsf{p}',
0x1d5ca: '\\mathsf{q}',
0x1d5cb: '\\mathsf{r}',
0x1d5cc: '\\mathsf{s}',
0x1d5cd: '\\mathsf{t}',
0x1d5ce: '\\mathsf{u}',
0x1d5cf: '\\mathsf{v}',
0x1d5d0: '\\mathsf{w}',
0x1d5d1: '\\mathsf{x}',
0x1d5d2: '\\mathsf{y}',
0x1d5d3: '\\mathsf{z}',
0x1d670: '\\mathtt{A}',
0x1d671: '\\mathtt{B}',
0x1d672: '\\mathtt{C}',
0x1d673: '\\mathtt{D}',
0x1d674: '\\mathtt{E}',
0x1d675: '\\mathtt{F}',
0x1d676: '\\mathtt{G}',
0x1d677: '\\mathtt{H}',
0x1d678: '\\mathtt{I}',
0x1d679: '\\mathtt{J}',
0x1d67a: '\\mathtt{K}',
0x1d67b: '\\mathtt{L}',
0x1d67c: '\\mathtt{M}',
0x1d67d: '\\mathtt{N}',
0x1d67e: '\\mathtt{O}',
0x1d67f: '\\mathtt{P}',
0x1d680: '\\mathtt{Q}',
0x1d681: '\\mathtt{R}',
0x1d682: '\\mathtt{S}',
0x1d683: '\\mathtt{T}',
0x1d684: '\\mathtt{U}',
0x1d685: '\\mathtt{V}',
0x1d686: '\\mathtt{W}',
0x1d687: '\\mathtt{X}',
0x1d688: '\\mathtt{Y}',
0x1d689: '\\mathtt{Z}',
0x1d68a: '\\mathtt{a}',
0x1d68b: '\\mathtt{b}',
0x1d68c: '\\mathtt{c}',
0x1d68d: '\\mathtt{d}',
0x1d68e: '\\mathtt{e}',
0x1d68f: '\\mathtt{f}',
0x1d690: '\\mathtt{g}',
0x1d691: '\\mathtt{h}',
0x1d692: '\\mathtt{i}',
0x1d693: '\\mathtt{j}',
0x1d694: '\\mathtt{k}',
0x1d695: '\\mathtt{l}',
0x1d696: '\\mathtt{m}',
0x1d697: '\\mathtt{n}',
0x1d698: '\\mathtt{o}',
0x1d699: '\\mathtt{p}',
0x1d69a: '\\mathtt{q}',
0x1d69b: '\\mathtt{r}',
0x1d69c: '\\mathtt{s}',
0x1d69d: '\\mathtt{t}',
0x1d69e: '\\mathtt{u}',
0x1d69f: '\\mathtt{v}',
0x1d6a0: '\\mathtt{w}',
0x1d6a1: '\\mathtt{x}',
0x1d6a2: '\\mathtt{y}',
0x1d6a3: '\\mathtt{z}',
0x1d6a4: '\\imath ',
0x1d6a5: '\\jmath ',
0x1d6aa: '\\mathbf{\\Gamma}',
0x1d6ab: '\\mathbf{\\Delta}',
0x1d6af: '\\mathbf{\\Theta}',
0x1d6b2: '\\mathbf{\\Lambda}',
0x1d6b5: '\\mathbf{\\Xi}',
0x1d6b7: '\\mathbf{\\Pi}',
0x1d6ba: '\\mathbf{\\Sigma}',
0x1d6bc: '\\mathbf{\\Upsilon}',
0x1d6bd: '\\mathbf{\\Phi}',
0x1d6bf: '\\mathbf{\\Psi}',
0x1d6c0: '\\mathbf{\\Omega}',
0x1d6e4: '\\mathit{\\Gamma}',
0x1d6e5: '\\mathit{\\Delta}',
0x1d6e9: '\\mathit{\\Theta}',
0x1d6ec: '\\mathit{\\Lambda}',
0x1d6ef: '\\mathit{\\Xi}',
0x1d6f1: '\\mathit{\\Pi}',
0x1d6f4: '\\mathit{\\Sigma}',
0x1d6f6: '\\mathit{\\Upsilon}',
0x1d6f7: '\\mathit{\\Phi}',
0x1d6f9: '\\mathit{\\Psi}',
0x1d6fa: '\\mathit{\\Omega}',
0x1d6fc: '\\alpha ',
0x1d6fd: '\\beta ',
0x1d6fe: '\\gamma ',
0x1d6ff: '\\delta ',
0x1d700: '\\varepsilon ',
0x1d701: '\\zeta ',
0x1d702: '\\eta ',
0x1d703: '\\theta ',
0x1d704: '\\iota ',
0x1d705: '\\kappa ',
0x1d706: '\\lambda ',
0x1d707: '\\mu ',
0x1d708: '\\nu ',
0x1d709: '\\xi ',
0x1d70b: '\\pi ',
0x1d70c: '\\rho ',
0x1d70d: '\\varsigma ',
0x1d70e: '\\sigma ',
0x1d70f: '\\tau ',
0x1d710: '\\upsilon ',
0x1d711: '\\varphi ',
0x1d712: '\\chi ',
0x1d713: '\\psi ',
0x1d714: '\\omega ',
0x1d715: '\\partial ',
0x1d716: '\\epsilon ',
0x1d717: '\\vartheta ',
0x1d718: '\\varkappa ',
0x1d719: '\\phi ',
0x1d71a: '\\varrho ',
0x1d71b: '\\varpi ',
0x1d7ce: '\\mathbf{0}',
0x1d7cf: '\\mathbf{1}',
0x1d7d0: '\\mathbf{2}',
0x1d7d1: '\\mathbf{3}',
0x1d7d2: '\\mathbf{4}',
0x1d7d3: '\\mathbf{5}',
0x1d7d4: '\\mathbf{6}',
0x1d7d5: '\\mathbf{7}',
0x1d7d6: '\\mathbf{8}',
0x1d7d7: '\\mathbf{9}',
0x1d7e2: '\\mathsf{0}',
0x1d7e3: '\\mathsf{1}',
0x1d7e4: '\\mathsf{2}',
0x1d7e5: '\\mathsf{3}',
0x1d7e6: '\\mathsf{4}',
0x1d7e7: '\\mathsf{5}',
0x1d7e8: '\\mathsf{6}',
0x1d7e9: '\\mathsf{7}',
0x1d7ea: '\\mathsf{8}',
0x1d7eb: '\\mathsf{9}',
0x1d7f6: '\\mathtt{0}',
0x1d7f7: '\\mathtt{1}',
0x1d7f8: '\\mathtt{2}',
0x1d7f9: '\\mathtt{3}',
0x1d7fa: '\\mathtt{4}',
0x1d7fb: '\\mathtt{5}',
0x1d7fc: '\\mathtt{6}',
0x1d7fd: '\\mathtt{7}',
0x1d7fe: '\\mathtt{8}',
0x1d7ff: '\\mathtt{9}',
}
