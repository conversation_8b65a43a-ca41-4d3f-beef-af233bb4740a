.. This data file has been placed in the public domain.
.. Derived from the Unicode character mappings available from
   <http://www.w3.org/2003/entities/xml/>.
   Processed by unicode2rstsubs.py, part of Docutils:
   <https://docutils.sourceforge.io>.

.. |gnap|     unicode:: U+02A8A .. GREATER-THAN AND NOT APPROXIMATE
.. |gnE|      unicode:: U+02269 .. GREATER-THAN BUT NOT EQUAL TO
.. |gne|      unicode:: U+02A88 .. GREATER-THAN AND SINGLE-LINE NOT EQUAL TO
.. |gnsim|    unicode:: U+022E7 .. GREATER-THAN BUT NOT EQUIVALENT TO
.. |gvnE|     unicode:: U+02269 U+0FE00 .. GREATER-THAN BUT NOT EQUAL TO - with vertical stroke
.. |lnap|     unicode:: U+02A89 .. LESS-THAN AND NOT APPROXIMATE
.. |lnE|      unicode:: U+02268 .. LESS-THAN BUT NOT EQUAL TO
.. |lne|      unicode:: U+02A87 .. LESS-THAN AND SINGLE-LINE NOT EQUAL TO
.. |lnsim|    unicode:: U+022E6 .. LESS-THAN BUT NOT EQUIVALENT TO
.. |lvnE|     unicode:: U+02268 U+0FE00 .. LESS-THAN BUT NOT EQUAL TO - with vertical stroke
.. |nap|      unicode:: U+02249 .. NOT ALMOST EQUAL TO
.. |napE|     unicode:: U+02A70 U+00338 .. APPROXIMATELY EQUAL OR EQUAL TO with slash
.. |napid|    unicode:: U+0224B U+00338 .. TRIPLE TILDE with slash
.. |ncong|    unicode:: U+02247 .. NEITHER APPROXIMATELY NOR ACTUALLY EQUAL TO
.. |ncongdot| unicode:: U+02A6D U+00338 .. CONGRUENT WITH DOT ABOVE with slash
.. |nequiv|   unicode:: U+02262 .. NOT IDENTICAL TO
.. |ngE|      unicode:: U+02267 U+00338 .. GREATER-THAN OVER EQUAL TO with slash
.. |nge|      unicode:: U+02271 .. NEITHER GREATER-THAN NOR EQUAL TO
.. |nges|     unicode:: U+02A7E U+00338 .. GREATER-THAN OR SLANTED EQUAL TO with slash
.. |nGg|      unicode:: U+022D9 U+00338 .. VERY MUCH GREATER-THAN with slash
.. |ngsim|    unicode:: U+02275 .. NEITHER GREATER-THAN NOR EQUIVALENT TO
.. |nGt|      unicode:: U+0226B U+020D2 .. MUCH GREATER THAN with vertical line
.. |ngt|      unicode:: U+0226F .. NOT GREATER-THAN
.. |nGtv|     unicode:: U+0226B U+00338 .. MUCH GREATER THAN with slash
.. |nlE|      unicode:: U+02266 U+00338 .. LESS-THAN OVER EQUAL TO with slash
.. |nle|      unicode:: U+02270 .. NEITHER LESS-THAN NOR EQUAL TO
.. |nles|     unicode:: U+02A7D U+00338 .. LESS-THAN OR SLANTED EQUAL TO with slash
.. |nLl|      unicode:: U+022D8 U+00338 .. VERY MUCH LESS-THAN with slash
.. |nlsim|    unicode:: U+02274 .. NEITHER LESS-THAN NOR EQUIVALENT TO
.. |nLt|      unicode:: U+0226A U+020D2 .. MUCH LESS THAN with vertical line
.. |nlt|      unicode:: U+0226E .. NOT LESS-THAN
.. |nltri|    unicode:: U+022EA .. NOT NORMAL SUBGROUP OF
.. |nltrie|   unicode:: U+022EC .. NOT NORMAL SUBGROUP OF OR EQUAL TO
.. |nLtv|     unicode:: U+0226A U+00338 .. MUCH LESS THAN with slash
.. |nmid|     unicode:: U+02224 .. DOES NOT DIVIDE
.. |npar|     unicode:: U+02226 .. NOT PARALLEL TO
.. |npr|      unicode:: U+02280 .. DOES NOT PRECEDE
.. |nprcue|   unicode:: U+022E0 .. DOES NOT PRECEDE OR EQUAL
.. |npre|     unicode:: U+02AAF U+00338 .. PRECEDES ABOVE SINGLE-LINE EQUALS SIGN with slash
.. |nrtri|    unicode:: U+022EB .. DOES NOT CONTAIN AS NORMAL SUBGROUP
.. |nrtrie|   unicode:: U+022ED .. DOES NOT CONTAIN AS NORMAL SUBGROUP OR EQUAL
.. |nsc|      unicode:: U+02281 .. DOES NOT SUCCEED
.. |nsccue|   unicode:: U+022E1 .. DOES NOT SUCCEED OR EQUAL
.. |nsce|     unicode:: U+02AB0 U+00338 .. SUCCEEDS ABOVE SINGLE-LINE EQUALS SIGN with slash
.. |nsim|     unicode:: U+02241 .. NOT TILDE
.. |nsime|    unicode:: U+02244 .. NOT ASYMPTOTICALLY EQUAL TO
.. |nsmid|    unicode:: U+02224 .. DOES NOT DIVIDE
.. |nspar|    unicode:: U+02226 .. NOT PARALLEL TO
.. |nsqsube|  unicode:: U+022E2 .. NOT SQUARE IMAGE OF OR EQUAL TO
.. |nsqsupe|  unicode:: U+022E3 .. NOT SQUARE ORIGINAL OF OR EQUAL TO
.. |nsub|     unicode:: U+02284 .. NOT A SUBSET OF
.. |nsubE|    unicode:: U+02AC5 U+00338 .. SUBSET OF ABOVE EQUALS SIGN with slash
.. |nsube|    unicode:: U+02288 .. NEITHER A SUBSET OF NOR EQUAL TO
.. |nsup|     unicode:: U+02285 .. NOT A SUPERSET OF
.. |nsupE|    unicode:: U+02AC6 U+00338 .. SUPERSET OF ABOVE EQUALS SIGN with slash
.. |nsupe|    unicode:: U+02289 .. NEITHER A SUPERSET OF NOR EQUAL TO
.. |ntgl|     unicode:: U+02279 .. NEITHER GREATER-THAN NOR LESS-THAN
.. |ntlg|     unicode:: U+02278 .. NEITHER LESS-THAN NOR GREATER-THAN
.. |nvap|     unicode:: U+0224D U+020D2 .. EQUIVALENT TO with vertical line
.. |nVDash|   unicode:: U+022AF .. NEGATED DOUBLE VERTICAL BAR DOUBLE RIGHT TURNSTILE
.. |nVdash|   unicode:: U+022AE .. DOES NOT FORCE
.. |nvDash|   unicode:: U+022AD .. NOT TRUE
.. |nvdash|   unicode:: U+022AC .. DOES NOT PROVE
.. |nvge|     unicode:: U+02265 U+020D2 .. GREATER-THAN OR EQUAL TO with vertical line
.. |nvgt|     unicode:: U+0003E U+020D2 .. GREATER-THAN SIGN with vertical line
.. |nvle|     unicode:: U+02264 U+020D2 .. LESS-THAN OR EQUAL TO with vertical line
.. |nvlt|     unicode:: U+0003C U+020D2 .. LESS-THAN SIGN with vertical line
.. |nvltrie|  unicode:: U+022B4 U+020D2 .. NORMAL SUBGROUP OF OR EQUAL TO with vertical line
.. |nvrtrie|  unicode:: U+022B5 U+020D2 .. CONTAINS AS NORMAL SUBGROUP OR EQUAL TO with vertical line
.. |nvsim|    unicode:: U+0223C U+020D2 .. TILDE OPERATOR with vertical line
.. |parsim|   unicode:: U+02AF3 .. PARALLEL WITH TILDE OPERATOR
.. |prnap|    unicode:: U+02AB9 .. PRECEDES ABOVE NOT ALMOST EQUAL TO
.. |prnE|     unicode:: U+02AB5 .. PRECEDES ABOVE NOT EQUAL TO
.. |prnsim|   unicode:: U+022E8 .. PRECEDES BUT NOT EQUIVALENT TO
.. |rnmid|    unicode:: U+02AEE .. DOES NOT DIVIDE WITH REVERSED NEGATION SLASH
.. |scnap|    unicode:: U+02ABA .. SUCCEEDS ABOVE NOT ALMOST EQUAL TO
.. |scnE|     unicode:: U+02AB6 .. SUCCEEDS ABOVE NOT EQUAL TO
.. |scnsim|   unicode:: U+022E9 .. SUCCEEDS BUT NOT EQUIVALENT TO
.. |simne|    unicode:: U+02246 .. APPROXIMATELY BUT NOT ACTUALLY EQUAL TO
.. |solbar|   unicode:: U+0233F .. APL FUNCTIONAL SYMBOL SLASH BAR
.. |subnE|    unicode:: U+02ACB .. SUBSET OF ABOVE NOT EQUAL TO
.. |subne|    unicode:: U+0228A .. SUBSET OF WITH NOT EQUAL TO
.. |supnE|    unicode:: U+02ACC .. SUPERSET OF ABOVE NOT EQUAL TO
.. |supne|    unicode:: U+0228B .. SUPERSET OF WITH NOT EQUAL TO
.. |vnsub|    unicode:: U+02282 U+020D2 .. SUBSET OF with vertical line
.. |vnsup|    unicode:: U+02283 U+020D2 .. SUPERSET OF with vertical line
.. |vsubnE|   unicode:: U+02ACB U+0FE00 .. SUBSET OF ABOVE NOT EQUAL TO - variant with stroke through bottom members
.. |vsubne|   unicode:: U+0228A U+0FE00 .. SUBSET OF WITH NOT EQUAL TO - variant with stroke through bottom members
.. |vsupnE|   unicode:: U+02ACC U+0FE00 .. SUPERSET OF ABOVE NOT EQUAL TO - variant with stroke through bottom members
.. |vsupne|   unicode:: U+0228B U+0FE00 .. SUPERSET OF WITH NOT EQUAL TO - variant with stroke through bottom members
