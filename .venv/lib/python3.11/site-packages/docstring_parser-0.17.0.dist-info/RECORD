docstring_parser-0.17.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
docstring_parser-0.17.0.dist-info/METADATA,sha256=gbIZ88nEPrD7xSgJvJPgY2Bww7SClG2IunaNdbjPgP0,3499
docstring_parser-0.17.0.dist-info/RECORD,,
docstring_parser-0.17.0.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
docstring_parser-0.17.0.dist-info/licenses/LICENSE.md,sha256=3-UUozeuhBer0xqK9we71hcrA-VDC7CD4UWJnql6Puo,1084
docstring_parser/__init__.py,sha256=78WKoStdo7zvCcEU1Sm2zLvU_BRGKyTYjlScFEB14nk,695
docstring_parser/__pycache__/__init__.cpython-311.pyc,,
docstring_parser/__pycache__/attrdoc.cpython-311.pyc,,
docstring_parser/__pycache__/common.cpython-311.pyc,,
docstring_parser/__pycache__/epydoc.cpython-311.pyc,,
docstring_parser/__pycache__/google.cpython-311.pyc,,
docstring_parser/__pycache__/numpydoc.cpython-311.pyc,,
docstring_parser/__pycache__/parser.cpython-311.pyc,,
docstring_parser/__pycache__/rest.cpython-311.pyc,,
docstring_parser/__pycache__/util.cpython-311.pyc,,
docstring_parser/attrdoc.py,sha256=grazLW9kqFIFmvjP9waYtTjgUEurlrzBZBPMED4yRNk,4126
docstring_parser/common.py,sha256=Wua16UpvZL8nqmrRMwph2PPNDx774aK3WFnSnjvVPPw,6320
docstring_parser/epydoc.py,sha256=6onvURmJ6gSz5DK7r-p7LkiOlSjRQitkI2mNFIHPQQE,8923
docstring_parser/google.py,sha256=jhrRS_v8eOVO19ciQ0OZt3AH9LN_2m8XD6YmIRPQ6SQ,13526
docstring_parser/numpydoc.py,sha256=m_K2_icSjwzXnx14dDGiL9wxQ7IXnVT4pzQaLbeWTt8,15984
docstring_parser/parser.py,sha256=AB-R7X36siUZ1rqRQC3jtZKgAQJnkUGdPDjRJb54y9k,2938
docstring_parser/py.typed,sha256=bWew9mHgMy8LqMu7RuqQXFXLBxh2CRx0dUbSx-3wE48,27
docstring_parser/rest.py,sha256=CVvGjiXtjteL0DA-Oa2ySbHF91RctX8NggfprEHT990,8289
docstring_parser/tests/__init__.py,sha256=6VULghkufHkqtZfyiBamwZQNBA3z7f4TMEcBclUqTKE,34
docstring_parser/tests/__pycache__/__init__.cpython-311.pyc,,
docstring_parser/tests/__pycache__/_pydoctor.cpython-311.pyc,,
docstring_parser/tests/__pycache__/test_epydoc.cpython-311.pyc,,
docstring_parser/tests/__pycache__/test_google.cpython-311.pyc,,
docstring_parser/tests/__pycache__/test_numpydoc.cpython-311.pyc,,
docstring_parser/tests/__pycache__/test_parse_from_object.cpython-311.pyc,,
docstring_parser/tests/__pycache__/test_parser.cpython-311.pyc,,
docstring_parser/tests/__pycache__/test_rest.cpython-311.pyc,,
docstring_parser/tests/__pycache__/test_util.cpython-311.pyc,,
docstring_parser/tests/_pydoctor.py,sha256=nosAlcm_ovpetXTWnrLewqnP4Ea7pM_xbABAspBivcc,770
docstring_parser/tests/test_epydoc.py,sha256=h6aghJc4KAF3sJD5KhwLqTyXTpp5GCXEOFUTuyVLCQo,19097
docstring_parser/tests/test_google.py,sha256=oVBWi5qWu0VABXp1XYsSh8merbpR-rcZIAdCsURUrcw,27342
docstring_parser/tests/test_numpydoc.py,sha256=qRfcoqwPHPxP83MXAgV1mLgi3Vu4vSb2EgK3SRuj9Uc,28988
docstring_parser/tests/test_parse_from_object.py,sha256=n3gHsGJ8zAfqI6-4C_IyX2egrWF_-LSx9Nn59GjvVeE,3824
docstring_parser/tests/test_parser.py,sha256=6UuVKFTj7501CzjdA8A-mrpRS63hA8Wor-_H3vsksvo,6700
docstring_parser/tests/test_rest.py,sha256=V_-z1pfAxTBabOkP9e3hm_xQGEPyfPxo34b5FyxrnEE,15195
docstring_parser/tests/test_util.py,sha256=rN8vMUKfyMmKdDxKV6RrjPXPPQclmX7wcN6aSrj4NFI,1739
docstring_parser/util.py,sha256=8VmGXhuMcWkrvtZSSKIhX4cKakJmWIILffY6JBQAiCg,4506
